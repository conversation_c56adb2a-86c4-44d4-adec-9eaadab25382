/*
 * SAX2.c : 用于构建树的默认SAX2处理器。
 *
 *
 *
 *
 */


#define IN_LIBXML
#include "libxml.h"
#include <stdlib.h>
#include <string.h>
#include <limits.h>
#include <stddef.h>
#include <libxml/SAX2.h>
#include <libxml/xmlmemory.h>
#include <libxml/tree.h>
#include <libxml/parser.h>
#include <libxml/parserInternals.h>
#include <libxml/valid.h>
#include <libxml/entities.h>
#include <libxml/xmlerror.h>
#include <libxml/xmlIO.h>
#include <libxml/uri.h>
#include <libxml/valid.h>
#include <libxml/HTMLtree.h>

#include "private/error.h"
#include "private/parser.h"
#include "private/tree.h"

/*
 * @param ctxt  XML验证解析器上下文
 * @param msg  伴随错误消息的字符串
 */
static void
xmlSAX2ErrMemory(xmlParserCtxtPtr ctxt) {
    xmlCtxtErrMemory(ctxt);
}

/**
 * 处理验证错误
 *
 * @param ctxt  XML验证解析器上下文
 * @param error  错误编号
 * @param msg  错误消息
 * @param str1  额外数据
 * @param str2  额外数据
 */
static void LIBXML_ATTR_FORMAT(3,0)
xmlErrValid(xmlParserCtxtPtr ctxt, xmlParserErrors error,
            const char *msg, const xmlChar *str1, const xmlChar *str2)
{
    xmlCtxtErr(ctxt, NULL, XML_FROM_DTD, error, XML_ERR_ERROR,
               str1, str2, NULL, 0, msg, str1, str2);
    if (ctxt != NULL)
	ctxt->valid = 0;
}

/**
 * 处理致命解析器错误，即违反良构性约束
 *
 * @param ctxt  XML解析器上下文
 * @param error  错误编号
 * @param msg  错误消息
 * @param str1  错误字符串
 * @param str2  错误字符串
 */
static void LIBXML_ATTR_FORMAT(3,0)
xmlFatalErrMsg(xmlParserCtxtPtr ctxt, xmlParserErrors error,
               const char *msg, const xmlChar *str1, const xmlChar *str2)
{
    xmlCtxtErr(ctxt, NULL, XML_FROM_PARSER, error, XML_ERR_FATAL,
               str1, str2, NULL, 0, msg, str1, str2);
}

/**
 * 处理解析器警告
 *
 * @param ctxt  XML解析器上下文
 * @param error  错误编号
 * @param msg  错误消息
 * @param str1  错误字符串
 */
static void LIBXML_ATTR_FORMAT(3,0)
xmlWarnMsg(xmlParserCtxtPtr ctxt, xmlParserErrors error,
               const char *msg, const xmlChar *str1)
{
    xmlCtxtErr(ctxt, NULL, XML_FROM_PARSER, error, XML_ERR_WARNING,
               str1, NULL, NULL, 0, msg, str1);
}

/**
 * 处理命名空间警告
 *
 * @param ctxt  XML解析器上下文
 * @param error  错误编号
 * @param msg  错误消息
 * @param str1  错误字符串
 * @param str2  错误字符串
 */
static void LIBXML_ATTR_FORMAT(3,0)
xmlNsWarnMsg(xmlParserCtxtPtr ctxt, xmlParserErrors error,
             const char *msg, const xmlChar *str1, const xmlChar *str2)
{
    xmlCtxtErr(ctxt, NULL, XML_FROM_NAMESPACE, error, XML_ERR_WARNING,
               str1, str2, NULL, 0, msg, str1, str2);
}

/**
 * 提供公共ID，例如 "-//SGMLSOURCE//DTD DEMO//EN"
 *
 * @param ctx  用户数据（XML解析器上下文）
 * @returns xmlChar *
 */
const xmlChar *
xmlSAX2GetPublicId(void *ctx ATTRIBUTE_UNUSED)
{
    /* xmlParserCtxtPtr ctxt = (xmlParserCtxtPtr) ctx; */
    return(NULL);
}

/**
 * 提供系统ID，基本上是URL或文件名，例如
 * http://www.sgmlsource.com/dtds/memo.dtd
 *
 * @param ctx  用户数据（XML解析器上下文）
 * @returns xmlChar *
 */
const xmlChar *
xmlSAX2GetSystemId(void *ctx)
{
    xmlParserCtxtPtr ctxt = (xmlParserCtxtPtr) ctx;
    if ((ctx == NULL) || (ctxt->input == NULL)) return(NULL);
    return((const xmlChar *) ctxt->input->filename);
}

/**
 * 提供当前解析点的行号。
 *
 * @param ctx  用户数据（XML解析器上下文）
 * @returns int
 */
int
xmlSAX2GetLineNumber(void *ctx)
{
    xmlParserCtxtPtr ctxt = (xmlParserCtxtPtr) ctx;
    if ((ctx == NULL) || (ctxt->input == NULL)) return(0);
    return(ctxt->input->line);
}

/**
 * 提供当前解析点的列号。
 *
 * @param ctx  用户数据（XML解析器上下文）
 * @returns int
 */
int
xmlSAX2GetColumnNumber(void *ctx)
{
    xmlParserCtxtPtr ctxt = (xmlParserCtxtPtr) ctx;
    if ((ctx == NULL) || (ctxt->input == NULL)) return(0);
    return(ctxt->input->col);
}

/**
 * 此文档是否标记为独立？
 *
 * @param ctx  用户数据（XML解析器上下文）
 * @returns 如果为真则返回1
 */
int
xmlSAX2IsStandalone(void *ctx)
{
    xmlParserCtxtPtr ctxt = (xmlParserCtxtPtr) ctx;
    if ((ctx == NULL) || (ctxt->myDoc == NULL)) return(0);
    return(ctxt->myDoc->standalone == 1);
}

/**
 * 此文档是否有内部子集
 *
 * @param ctx  用户数据（XML解析器上下文）
 * @returns 如果为真则返回1
 */
int
xmlSAX2HasInternalSubset(void *ctx)
{
    xmlParserCtxtPtr ctxt = (xmlParserCtxtPtr) ctx;
    if ((ctxt == NULL) || (ctxt->myDoc == NULL)) return(0);
    return(ctxt->myDoc->intSubset != NULL);
}

/**
 * 此文档是否有外部子集
 *
 * @param ctx  用户数据（XML解析器上下文）
 * @returns 如果为真则返回1
 */
int
xmlSAX2HasExternalSubset(void *ctx)
{
    xmlParserCtxtPtr ctxt = (xmlParserCtxtPtr) ctx;
    if ((ctxt == NULL) || (ctxt->myDoc == NULL)) return(0);
    return(ctxt->myDoc->extSubset != NULL);
}

/**
 * 内部子集声明的回调。
 *
 * @param ctx  用户数据（XML解析器上下文）
 * @param name  根元素名称
 * @param ExternalID  外部ID
 * @param SystemID  系统ID（例如文件名或URL）
 */
void
xmlSAX2InternalSubset(void *ctx, const xmlChar *name,
	       const xmlChar *ExternalID, const xmlChar *SystemID)
{
    xmlParserCtxtPtr ctxt = (xmlParserCtxtPtr) ctx;
    xmlDtdPtr dtd;
    if (ctx == NULL) return;

    if (ctxt->myDoc == NULL)
	return;
    if ((ctxt->html) && (ctxt->instate != XML_PARSER_MISC))
        return;
    dtd = xmlGetIntSubset(ctxt->myDoc);
    if (dtd != NULL) {
	xmlUnlinkNode((xmlNodePtr) dtd);
	xmlFreeDtd(dtd);
	ctxt->myDoc->intSubset = NULL;
    }
    ctxt->myDoc->intSubset =
	xmlCreateIntSubset(ctxt->myDoc, name, ExternalID, SystemID);
    if (ctxt->myDoc->intSubset == NULL)
        xmlSAX2ErrMemory(ctxt);
}

/**
 * 外部子集声明的回调。
 *
 * @param ctx  用户数据（XML解析器上下文）
 * @param name  根元素名称
 * @param ExternalID  外部ID
 * @param SystemID  系统ID（例如文件名或URL）
 */
void
xmlSAX2ExternalSubset(void *ctx, const xmlChar *name,
	       const xmlChar *ExternalID, const xmlChar *SystemID)
{
    xmlParserCtxtPtr ctxt = (xmlParserCtxtPtr) ctx;
    if (ctx == NULL) return;
    if ((SystemID != NULL) &&
        ((ctxt->options & XML_PARSE_NO_XXE) == 0) &&
        (((ctxt->validate) || (ctxt->loadsubset)) &&
	 (ctxt->wellFormed && ctxt->myDoc))) {
	/*
	 * 尝试获取并解析外部子集。
	 */
	xmlParserInputPtr oldinput;
	int oldinputNr;
	int oldinputMax;
	xmlParserInputPtr *oldinputTab;
	xmlParserInputPtr input = NULL;
	const xmlChar *oldencoding;
        unsigned long consumed;
        size_t buffered;
#ifdef FUZZING_BUILD_MODE_UNSAFE_FOR_PRODUCTION
        int inputMax = 1;
#else
        int inputMax = 5;
#endif

	/*
	 * 请求实体解析器加载这个东西
	 */
	if ((ctxt->sax != NULL) && (ctxt->sax->resolveEntity != NULL))
	    input = ctxt->sax->resolveEntity(ctxt->userData, ExternalID,
	                                        SystemID);
	if (input == NULL) {
	    return;
	}

	if (xmlNewDtd(ctxt->myDoc, name, ExternalID, SystemID) == NULL) {
            xmlSAX2ErrMemory(ctxt);
            xmlFreeInputStream(input);
            return;
        }

	/*
	 * 确保我们不会破坏主文档上下文
	 */
	oldinput = ctxt->input;
	oldinputNr = ctxt->inputNr;
	oldinputMax = ctxt->inputMax;
	oldinputTab = ctxt->inputTab;
	oldencoding = ctxt->encoding;
	ctxt->encoding = NULL;

	ctxt->inputTab = xmlMalloc(inputMax * sizeof(xmlParserInputPtr));
	if (ctxt->inputTab == NULL) {
	    xmlSAX2ErrMemory(ctxt);
            goto error;
	}
	ctxt->inputNr = 0;
	ctxt->inputMax = inputMax;
	ctxt->input = NULL;
	if (xmlCtxtPushInput(ctxt, input) < 0)
            goto error;

	if (input->filename == NULL)
	    input->filename = (char *) xmlCanonicPath(SystemID);
	input->line = 1;
	input->col = 1;
	input->base = ctxt->input->cur;
	input->cur = ctxt->input->cur;
	input->free = NULL;

	/*
	 * 让我们解析这个实体，知道它是一个外部子集。
	 */
	xmlParseExternalSubset(ctxt, ExternalID, SystemID);

        /*
	 * 释放外部实体
	 */

	while (ctxt->inputNr > 1)
	    xmlFreeInputStream(xmlCtxtPopInput(ctxt));

        consumed = ctxt->input->consumed;
        buffered = ctxt->input->cur - ctxt->input->base;
        if (buffered > ULONG_MAX - consumed)
            consumed = ULONG_MAX;
        else
            consumed += buffered;
        if (consumed > ULONG_MAX - ctxt->sizeentities)
            ctxt->sizeentities = ULONG_MAX;
        else
            ctxt->sizeentities += consumed;

error:
	xmlFreeInputStream(input);
        xmlFree(ctxt->inputTab);

	/*
	 * 恢复主实体的解析上下文
	 */
	ctxt->input = oldinput;
	ctxt->inputNr = oldinputNr;
	ctxt->inputMax = oldinputMax;
	ctxt->inputTab = oldinputTab;
	if ((ctxt->encoding != NULL) &&
	    ((ctxt->dict == NULL) ||
	     (!xmlDictOwns(ctxt->dict, ctxt->encoding))))
	    xmlFree((xmlChar *) ctxt->encoding);
	ctxt->encoding = oldencoding;
	/* ctxt->wellFormed = oldwellFormed; */
    }
}

/**
 * 这仅用于加载DTD。安装自定义解析器的首选方法是xmlCtxtSetResourceLoader()。
 *
 * @param ctx  用户数据（XML解析器上下文）
 * @param publicId  实体的公共ID
 * @param systemId  实体的系统ID
 * @returns 解析器输入。
 */
xmlParserInputPtr
xmlSAX2ResolveEntity(void *ctx, const xmlChar *publicId,
                     const xmlChar *systemId)
{
    xmlParserCtxtPtr ctxt = (xmlParserCtxtPtr) ctx;
    xmlParserInputPtr ret = NULL;
    xmlChar *URI = NULL;

    if (ctx == NULL) return(NULL);

    if (systemId != NULL) {
        const xmlChar *base = NULL;
        int res;

        if (ctxt->input != NULL)
            base = BAD_CAST ctxt->input->filename;

        /*
         * 我们实际上不需要'directory'结构成员，但一些
         * 用户为内存流手动将其设置为基础URI。
         */
        if (base == NULL)
            base = BAD_CAST ctxt->directory;

        if ((xmlStrlen(systemId) > XML_MAX_URI_LENGTH) ||
            (xmlStrlen(base) > XML_MAX_URI_LENGTH)) {
            xmlFatalErr(ctxt, XML_ERR_RESOURCE_LIMIT, "URI too long");
            return(NULL);
        }
        res = xmlBuildURISafe(systemId, base, &URI);
        if (URI == NULL) {
            if (res < 0)
                xmlSAX2ErrMemory(ctxt);
            else
                xmlWarnMsg(ctxt, XML_ERR_INVALID_URI,
                           "Can't resolve URI: %s\n", systemId);
            return(NULL);
        }
        if (xmlStrlen(URI) > XML_MAX_URI_LENGTH) {
            xmlFatalErr(ctxt, XML_ERR_RESOURCE_LIMIT, "URI too long");
            xmlFree(URI);
            return(NULL);
        }
    }

    ret = xmlLoadResource(ctxt, (const char *) URI,
                          (const char *) publicId, XML_RESOURCE_DTD);

    xmlFree(URI);
    return(ret);
}

/**
 * 按名称获取实体
 *
 * @param ctx  用户数据（XML解析器上下文）
 * @param name  实体名称
 * @returns 如果找到则返回xmlEntityPtr。
 */
xmlEntityPtr
xmlSAX2GetEntity(void *ctx, const xmlChar *name)
{
    xmlParserCtxtPtr ctxt = (xmlParserCtxtPtr) ctx;
    xmlEntityPtr ret = NULL;

    if (ctx == NULL) return(NULL);

    if (ctxt->inSubset == 0) {
	ret = xmlGetPredefinedEntity(name);
	if (ret != NULL)
	    return(ret);
    }
    if ((ctxt->myDoc != NULL) && (ctxt->myDoc->standalone == 1)) {
	if (ctxt->inSubset == 2) {
	    ctxt->myDoc->standalone = 0;
	    ret = xmlGetDocEntity(ctxt->myDoc, name);
	    ctxt->myDoc->standalone = 1;
	} else {
	    ret = xmlGetDocEntity(ctxt->myDoc, name);
	    if (ret == NULL) {
		ctxt->myDoc->standalone = 0;
		ret = xmlGetDocEntity(ctxt->myDoc, name);
		if (ret != NULL) {
		    xmlFatalErrMsg(ctxt, XML_ERR_NOT_STANDALONE,
	 "Entity(%s) document marked standalone but requires external subset\n",
				   name, NULL);
		}
		ctxt->myDoc->standalone = 1;
	    }
	}
    } else {
	ret = xmlGetDocEntity(ctxt->myDoc, name);
    }
    return(ret);
}

/**
 * 按名称获取参数实体
 *
 * @param ctx  用户数据（XML解析器上下文）
 * @param name  实体名称
 * @returns 如果找到则返回xmlEntityPtr。
 */
xmlEntityPtr
xmlSAX2GetParameterEntity(void *ctx, const xmlChar *name)
{
    xmlParserCtxtPtr ctxt = (xmlParserCtxtPtr) ctx;
    xmlEntityPtr ret;

    if (ctx == NULL) return(NULL);

    ret = xmlGetParameterEntity(ctxt->myDoc, name);
    return(ret);
}


/**
 * 已解析实体定义
 *
 * @param ctx  用户数据（XML解析器上下文）
 * @param name  实体名称
 * @param type  实体类型
 * @param publicId  实体的公共ID
 * @param systemId  实体的系统ID
 * @param content  实体值（未处理）。
 */
void
xmlSAX2EntityDecl(void *ctx, const xmlChar *name, int type,
          const xmlChar *publicId, const xmlChar *systemId, xmlChar *content)
{
    xmlEntityPtr ent;
    xmlParserCtxtPtr ctxt = (xmlParserCtxtPtr) ctx;
    int extSubset;
    int res;

    if ((ctxt == NULL) || (ctxt->myDoc == NULL))
        return;

    extSubset = ctxt->inSubset == 2;
    res = xmlAddEntity(ctxt->myDoc, extSubset, name, type, publicId, systemId,
                       content, &ent);
    switch (res) {
        case XML_ERR_OK:
            break;
        case XML_ERR_NO_MEMORY:
            xmlSAX2ErrMemory(ctxt);
            return;
        case XML_WAR_ENTITY_REDEFINED:
            if (ctxt->pedantic) {
                if (extSubset)
                    xmlWarnMsg(ctxt, res, "Entity(%s) already defined in the"
                               " external subset\n", name);
                else
                    xmlWarnMsg(ctxt, res, "Entity(%s) already defined in the"
                               " internal subset\n", name);
            }
            return;
        case XML_ERR_REDECL_PREDEF_ENTITY:
            /*
             * 技术上是一个错误，但根据"4.6预定义实体"
             * 获得双重转义是一个常见错误。
             */
            xmlWarnMsg(ctxt, res, "Invalid redeclaration of predefined"
                       " entity '%s'", name);
            return;
        default:
            xmlFatalErrMsg(ctxt, XML_ERR_INTERNAL_ERROR,
                           "Unexpected error code from xmlAddEntity\n",
                           NULL, NULL);
            return;
    }

    if ((ent->URI == NULL) && (systemId != NULL)) {
        xmlChar *URI;
        const char *base = NULL;
        int i;

        for (i = ctxt->inputNr - 1; i >= 0; i--) {
            if (ctxt->inputTab[i]->filename != NULL) {
                base = ctxt->inputTab[i]->filename;
                break;
            }
        }

        /*
         * 我们实际上不需要'directory'结构成员，但一些
         * 用户为内存流手动将其设置为基础URI。
         */
        if (base == NULL)
            base = ctxt->directory;

        res = xmlBuildURISafe(systemId, (const xmlChar *) base, &URI);

        if (URI == NULL) {
            if (res < 0) {
                xmlSAX2ErrMemory(ctxt);
            } else {
                xmlWarnMsg(ctxt, XML_ERR_INVALID_URI,
                           "Can't resolve URI: %s\n", systemId);
            }
        } else if (xmlStrlen(URI) > XML_MAX_URI_LENGTH) {
            xmlFatalErr(ctxt, XML_ERR_RESOURCE_LIMIT, "URI too long");
            xmlFree(URI);
        } else {
            ent->URI = URI;
        }
    }
}

/**
 * 已解析属性定义
 *
 * @param ctx  用户数据（XML解析器上下文）
 * @param elem  元素名称
 * @param fullname  属性名称
 * @param type  属性类型
 * @param def  默认值类型
 * @param defaultValue  属性默认值
 * @param tree  枚举值集的树
 */
void
xmlSAX2AttributeDecl(void *ctx, const xmlChar *elem, const xmlChar *fullname,
              int type, int def, const xmlChar *defaultValue,
	      xmlEnumerationPtr tree)
{
    xmlParserCtxtPtr ctxt = (xmlParserCtxtPtr) ctx;
    xmlAttributePtr attr;
    const xmlChar *name = NULL;
    xmlChar *prefix = NULL;

    /* 如果功能被禁用，避免未使用变量警告。 */
    (void) attr;

    if ((ctxt == NULL) || (ctxt->myDoc == NULL))
        return;

    if ((xmlStrEqual(fullname, BAD_CAST "xml:id")) &&
        (type != XML_ATTRIBUTE_ID)) {
	/*
	 * 引发错误但保持有效性标志
	 */
	int tmp = ctxt->valid;
	xmlErrValid(ctxt, XML_DTD_XMLID_TYPE,
	      "xml:id : attribute type should be ID\n", NULL, NULL);
	ctxt->valid = tmp;
    }
    name = xmlSplitQName4(fullname, &prefix);
    if (name == NULL)
        xmlSAX2ErrMemory(ctxt);
    ctxt->vctxt.valid = 1;
    if (ctxt->inSubset == 1)
	attr = xmlAddAttributeDecl(&ctxt->vctxt, ctxt->myDoc->intSubset, elem,
	       name, prefix, (xmlAttributeType) type,
	       (xmlAttributeDefault) def, defaultValue, tree);
    else if (ctxt->inSubset == 2)
	attr = xmlAddAttributeDecl(&ctxt->vctxt, ctxt->myDoc->extSubset, elem,
	   name, prefix, (xmlAttributeType) type,
	   (xmlAttributeDefault) def, defaultValue, tree);
    else {
        xmlFatalErrMsg(ctxt, XML_ERR_INTERNAL_ERROR,
	     "SAX.xmlSAX2AttributeDecl(%s) called while not in subset\n",
	               name, NULL);
	xmlFree(prefix);
	xmlFreeEnumeration(tree);
	return;
    }
#ifdef LIBXML_VALID_ENABLED
    if (ctxt->vctxt.valid == 0)
	ctxt->valid = 0;
    if ((attr != NULL) && (ctxt->validate) && (ctxt->wellFormed) &&
        (ctxt->myDoc->intSubset != NULL))
	ctxt->valid &= xmlValidateAttributeDecl(&ctxt->vctxt, ctxt->myDoc,
	                                        attr);
#endif /* LIBXML_VALID_ENABLED */
    if (prefix != NULL)
	xmlFree(prefix);
}

/**
 * 已解析元素定义
 *
 * @param ctx  用户数据（XML解析器上下文）
 * @param name  元素名称
 * @param type  元素类型
 * @param content  元素值树
 */
void
xmlSAX2ElementDecl(void *ctx, const xmlChar * name, int type,
            xmlElementContentPtr content)
{
    xmlParserCtxtPtr ctxt = (xmlParserCtxtPtr) ctx;
    xmlElementPtr elem = NULL;

    /* 如果功能被禁用，避免未使用变量警告。 */
    (void) elem;

    if ((ctxt == NULL) || (ctxt->myDoc == NULL))
        return;

    if (ctxt->inSubset == 1)
        elem = xmlAddElementDecl(&ctxt->vctxt, ctxt->myDoc->intSubset,
                                 name, (xmlElementTypeVal) type, content);
    else if (ctxt->inSubset == 2)
        elem = xmlAddElementDecl(&ctxt->vctxt, ctxt->myDoc->extSubset,
                                 name, (xmlElementTypeVal) type, content);
    else {
        xmlFatalErrMsg(ctxt, XML_ERR_INTERNAL_ERROR,
	     "SAX.xmlSAX2ElementDecl(%s) called while not in subset\n",
	               name, NULL);
        return;
    }
#ifdef LIBXML_VALID_ENABLED
    if (elem == NULL)
        ctxt->valid = 0;
    if (ctxt->validate && ctxt->wellFormed &&
        ctxt->myDoc && ctxt->myDoc->intSubset)
        ctxt->valid &=
            xmlValidateElementDecl(&ctxt->vctxt, ctxt->myDoc, elem);
#endif /* LIBXML_VALID_ENABLED */
}

/**
 * 解析符号声明时要做什么。
 *
 * @param ctx  用户数据（XML解析器上下文）
 * @param name  符号名称
 * @param publicId  实体的公共ID
 * @param systemId  实体的系统ID
 */
void
xmlSAX2NotationDecl(void *ctx, const xmlChar *name,
	     const xmlChar *publicId, const xmlChar *systemId)
{
    xmlParserCtxtPtr ctxt = (xmlParserCtxtPtr) ctx;
    xmlNotationPtr nota = NULL;

    /* 如果功能被禁用，避免未使用变量警告。 */
    (void) nota;

    if ((ctxt == NULL) || (ctxt->myDoc == NULL))
        return;

    if ((publicId == NULL) && (systemId == NULL)) {
	xmlFatalErrMsg(ctxt, XML_ERR_NOTATION_PROCESSING,
	     "SAX.xmlSAX2NotationDecl(%s) externalID or PublicID missing\n",
	               name, NULL);
	return;
    } else if (ctxt->inSubset == 1)
	nota = xmlAddNotationDecl(&ctxt->vctxt, ctxt->myDoc->intSubset, name,
                              publicId, systemId);
    else if (ctxt->inSubset == 2)
	nota = xmlAddNotationDecl(&ctxt->vctxt, ctxt->myDoc->extSubset, name,
                              publicId, systemId);
    else {
	xmlFatalErrMsg(ctxt, XML_ERR_NOTATION_PROCESSING,
	     "SAX.xmlSAX2NotationDecl(%s) called while not in subset\n",
	               name, NULL);
	return;
    }
#ifdef LIBXML_VALID_ENABLED
    if (nota == NULL) ctxt->valid = 0;
    if ((ctxt->validate) && (ctxt->wellFormed) &&
        (ctxt->myDoc->intSubset != NULL))
	ctxt->valid &= xmlValidateNotationDecl(&ctxt->vctxt, ctxt->myDoc,
	                                       nota);
#endif /* LIBXML_VALID_ENABLED */
}

/**
 * 解析未解析实体声明时要做什么
 *
 * @param ctx  用户数据（XML解析器上下文）
 * @param name  实体名称
 * @param publicId  实体的公共ID
 * @param systemId  实体的系统ID
 * @param notationName  符号名称
 */
void
xmlSAX2UnparsedEntityDecl(void *ctx, const xmlChar *name,
		   const xmlChar *publicId, const xmlChar *systemId,
		   const xmlChar *notationName)
{
    xmlSAX2EntityDecl(ctx, name, XML_EXTERNAL_GENERAL_UNPARSED_ENTITY,
                      publicId, systemId, (xmlChar *) notationName);
}

/**
 * 在启动时接收文档定位器，实际上是xmlDefaultSAXLocator
 * 所有内容都在上下文中可用，所以在我们的情况下这是无用的。
 *
 * @param ctx  用户数据（XML解析器上下文）
 * @param loc  SAX定位器
 */
void
xmlSAX2SetDocumentLocator(void *ctx ATTRIBUTE_UNUSED, xmlSAXLocatorPtr loc ATTRIBUTE_UNUSED)
{
}

/**
 * 当文档开始被处理时调用。
 *
 * @param ctx  用户数据（XML解析器上下文）
 */
void
xmlSAX2StartDocument(void *ctx)
{
    xmlParserCtxtPtr ctxt = (xmlParserCtxtPtr) ctx;
    xmlDocPtr doc;

    if (ctx == NULL) return;

#ifdef LIBXML_HTML_ENABLED
    if (ctxt->html) {
	if (ctxt->myDoc == NULL)
	    ctxt->myDoc = htmlNewDocNoDtD(NULL, NULL);
	if (ctxt->myDoc == NULL) {
	    xmlSAX2ErrMemory(ctxt);
	    return;
	}
	ctxt->myDoc->properties = XML_DOC_HTML;
	ctxt->myDoc->parseFlags = ctxt->options;
    } else
#endif
    {
	doc = ctxt->myDoc = xmlNewDoc(ctxt->version);
	if (doc != NULL) {
	    doc->properties = 0;
	    if (ctxt->options & XML_PARSE_OLD10)
	        doc->properties |= XML_DOC_OLD10;
	    doc->parseFlags = ctxt->options;
	    doc->standalone = ctxt->standalone;
	} else {
	    xmlSAX2ErrMemory(ctxt);
	    return;
	}
	if ((ctxt->dictNames) && (doc != NULL)) {
	    doc->dict = ctxt->dict;
	    xmlDictReference(doc->dict);
	}
    }
    if ((ctxt->myDoc != NULL) && (ctxt->myDoc->URL == NULL) &&
	(ctxt->input != NULL) && (ctxt->input->filename != NULL)) {
	ctxt->myDoc->URL = xmlPathToURI((const xmlChar *)ctxt->input->filename);
	if (ctxt->myDoc->URL == NULL)
	    xmlSAX2ErrMemory(ctxt);
    }
}

/**
 * 当检测到文档结束时调用。
 *
 * @param ctx  用户数据（XML解析器上下文）
 */
void
xmlSAX2EndDocument(void *ctx)
{
    xmlParserCtxtPtr ctxt = (xmlParserCtxtPtr) ctx;
    xmlDocPtr doc;

    if (ctx == NULL) return;
#ifdef LIBXML_VALID_ENABLED
    if (ctxt->validate && ctxt->wellFormed &&
        ctxt->myDoc && ctxt->myDoc->intSubset)
	ctxt->valid &= xmlValidateDocumentFinal(&ctxt->vctxt, ctxt->myDoc);
#endif /* LIBXML_VALID_ENABLED */

    doc = ctxt->myDoc;
    if ((doc != NULL) && (doc->encoding == NULL)) {
        const xmlChar *encoding = xmlGetActualEncoding(ctxt);

        if (encoding != NULL) {
            doc->encoding = xmlStrdup(encoding);
            if (doc->encoding == NULL)
                xmlSAX2ErrMemory(ctxt);
        }
    }
}

static void
xmlSAX2AppendChild(xmlParserCtxtPtr ctxt, xmlNodePtr node) {
    xmlNodePtr parent;
    xmlNodePtr last;

    if (ctxt->inSubset == 1) {
	parent = (xmlNodePtr) ctxt->myDoc->intSubset;
    } else if (ctxt->inSubset == 2) {
	parent = (xmlNodePtr) ctxt->myDoc->extSubset;
    } else {
        parent = ctxt->node;
        if (parent == NULL)
            parent = (xmlNodePtr) ctxt->myDoc;
    }

    last = parent->last;
    if (last == NULL) {
        parent->children = node;
    } else {
        last->next = node;
        node->prev = last;
    }

    parent->last = node;
    node->parent = parent;

    if ((node->type != XML_TEXT_NODE) &&
        (ctxt->linenumbers) &&
	(ctxt->input != NULL)) {
        if ((unsigned) ctxt->input->line < (unsigned) USHRT_MAX)
            node->line = ctxt->input->line;
        else
            node->line = USHRT_MAX;
    }
}

#if defined(LIBXML_SAX1_ENABLED)
/**
 * 处理命名空间错误
 *
 * @param ctxt  XML解析器上下文
 * @param error  错误编号
 * @param msg  错误消息
 * @param str1  错误字符串
 * @param str2  错误字符串
 */
static void LIBXML_ATTR_FORMAT(3,0)
xmlNsErrMsg(xmlParserCtxtPtr ctxt, xmlParserErrors error,
            const char *msg, const xmlChar *str1, const xmlChar *str2)
{
    xmlCtxtErr(ctxt, NULL, XML_FROM_NAMESPACE, error, XML_ERR_ERROR,
               str1, str2, NULL, 0, msg, str1, str2);
}

/**
 * 处理解析器读取的属性。
 *
 * 已弃用的SAX1接口。
 * @param ctxt  解析器上下文
 * @param fullname  属性名称，包括命名空间前缀
 * @param value  属性值
 * @param prefix  命名空间前缀
 */
static void
xmlSAX1Attribute(xmlParserCtxtPtr ctxt, const xmlChar *fullname,
                 const xmlChar *value, const xmlChar *prefix)
{
    xmlAttrPtr ret;
    const xmlChar *name;
    xmlChar *ns;
    xmlNsPtr namespace;

    /*
     * 将完整名称拆分为命名空间前缀和标签名称
     */
    name = xmlSplitQName4(fullname, &ns);
    if (name == NULL) {
        xmlSAX2ErrMemory(ctxt);
        return;
    }

    /*
     * 检查是否为命名空间定义
     */
    if ((ns == NULL) &&
        (name[0] == 'x') && (name[1] == 'm') && (name[2] == 'l') &&
        (name[3] == 'n') && (name[4] == 's') && (name[5] == 0)) {
	xmlNsPtr nsret;
	xmlChar *val;

        /* 如果功能被禁用，避免未使用变量警告。 */
        (void) nsret;

        if (!ctxt->replaceEntities) {
            /*  如果需要则规范化 */
	    val = xmlExpandEntitiesInAttValue(ctxt, value, /* normalize */ 0);
	    if (val == NULL) {
	        xmlSAX2ErrMemory(ctxt);
		return;
	    }
	} else {
	    val = (xmlChar *) value;
	}

	if (val[0] != 0) {
	    xmlURIPtr uri;

	    if (xmlParseURISafe((const char *)val, &uri) < 0)
                xmlSAX2ErrMemory(ctxt);
	    if (uri == NULL) {
                xmlNsWarnMsg(ctxt, XML_WAR_NS_URI,
                             "xmlns:%s: %s not a valid URI\n", name, value);
	    } else {
		if (uri->scheme == NULL) {
                    xmlNsWarnMsg(ctxt, XML_WAR_NS_URI_RELATIVE,
                                 "xmlns:%s: URI %s is not absolute\n",
                                 name, value);
		}
		xmlFreeURI(uri);
	    }
	}

	/* 默认命名空间定义 */
	nsret = xmlNewNs(ctxt->node, val, NULL);
        if (nsret == NULL) {
            xmlSAX2ErrMemory(ctxt);
        }
#ifdef LIBXML_VALID_ENABLED
	/*
	 * 也要验证命名空间声明，从XML-1.0的角度来看它们是属性
	 */
        else if (ctxt->validate && ctxt->wellFormed &&
                 ctxt->myDoc && ctxt->myDoc->intSubset) {
	    ctxt->valid &= xmlValidateOneNamespace(&ctxt->vctxt, ctxt->myDoc,
					   ctxt->node, prefix, nsret, val);
        }
#endif /* LIBXML_VALID_ENABLED */
	if (val != value)
	    xmlFree(val);
	return;
    }
    if ((ns != NULL) && (ns[0] == 'x') && (ns[1] == 'm') && (ns[2] == 'l') &&
        (ns[3] == 'n') && (ns[4] == 's') && (ns[5] == 0)) {
	xmlNsPtr nsret;
	xmlChar *val;

        /* 如果功能被禁用，避免未使用变量警告。 */
        (void) nsret;

        if (!ctxt->replaceEntities) {
            /*  如果需要则规范化 */
	    val = xmlExpandEntitiesInAttValue(ctxt, value, /* normalize */ 0);
	    if (val == NULL) {
	        xmlSAX2ErrMemory(ctxt);
	        xmlFree(ns);
		return;
	    }
	} else {
	    val = (xmlChar *) value;
	}

	if (val[0] == 0) {
	    xmlNsErrMsg(ctxt, XML_NS_ERR_EMPTY,
		        "Empty namespace name for prefix %s\n", name, NULL);
	}
	if ((ctxt->pedantic != 0) && (val[0] != 0)) {
	    xmlURIPtr uri;

	    if (xmlParseURISafe((const char *)val, &uri) < 0)
                xmlSAX2ErrMemory(ctxt);
	    if (uri == NULL) {
	        xmlNsWarnMsg(ctxt, XML_WAR_NS_URI,
			 "xmlns:%s: %s not a valid URI\n", name, value);
	    } else {
		if (uri->scheme == NULL) {
		    xmlNsWarnMsg(ctxt, XML_WAR_NS_URI_RELATIVE,
			   "xmlns:%s: URI %s is not absolute\n", name, value);
		}
		xmlFreeURI(uri);
	    }
	}

	/* 标准命名空间定义 */
	nsret = xmlNewNs(ctxt->node, val, name);
	xmlFree(ns);

        if (nsret == NULL) {
            xmlSAX2ErrMemory(ctxt);
        }
#ifdef LIBXML_VALID_ENABLED
	/*
	 * 也要验证命名空间声明，从XML-1.0的角度来看它们是属性
	 */
        else if (ctxt->validate && ctxt->wellFormed &&
	         ctxt->myDoc && ctxt->myDoc->intSubset) {
	    ctxt->valid &= xmlValidateOneNamespace(&ctxt->vctxt, ctxt->myDoc,
					   ctxt->node, prefix, nsret, value);
        }
#endif /* LIBXML_VALID_ENABLED */
	if (val != value)
	    xmlFree(val);
	return;
    }

    if (ns != NULL) {
        int res;

	res = xmlSearchNsSafe(ctxt->node, ns, &namespace);
        if (res < 0)
            xmlSAX2ErrMemory(ctxt);

	if (namespace == NULL) {
	    xmlNsErrMsg(ctxt, XML_NS_ERR_UNDEFINED_NAMESPACE,
		    "Namespace prefix %s of attribute %s is not defined\n",
		             ns, name);
	} else {
            xmlAttrPtr prop;

            prop = ctxt->node->properties;
            while (prop != NULL) {
                if (prop->ns != NULL) {
                    if ((xmlStrEqual(name, prop->name)) &&
                        ((namespace == prop->ns) ||
                         (xmlStrEqual(namespace->href, prop->ns->href)))) {
                        xmlCtxtErr(ctxt, NULL, XML_FROM_PARSER,
                                   XML_ERR_ATTRIBUTE_REDEFINED, XML_ERR_FATAL,
                                   name, NULL, NULL, 0,
                                   "Attribute %s in %s redefined\n",
                                   name, namespace->href);
                        goto error;
                    }
                }
                prop = prop->next;
            }
        }
    } else {
	namespace = NULL;
    }

    /* !!!!!! <a toto:arg="" xmlns:toto="http://toto.com"> */
    ret = xmlNewNsProp(ctxt->node, namespace, name, NULL);
    if (ret == NULL) {
        xmlSAX2ErrMemory(ctxt);
        goto error;
    }

    if (ctxt->replaceEntities == 0) {
        if (xmlNodeParseContent((xmlNodePtr) ret, value, INT_MAX) < 0)
            xmlSAX2ErrMemory(ctxt);
    } else if (value != NULL) {
        ret->children = xmlNewDocText(ctxt->myDoc, value);
        if (ret->children == NULL) {
            xmlSAX2ErrMemory(ctxt);
        } else {
            ret->last = ret->children;
            ret->children->parent = (xmlNodePtr) ret;
        }
    }

#ifdef LIBXML_VALID_ENABLED
    if (ctxt->validate && ctxt->wellFormed &&
        ctxt->myDoc && ctxt->myDoc->intSubset) {

	/*
	 * 如果我们不替换实体，验证仍应在替换实体的值上进行。
	 */
        if (!ctxt->replaceEntities) {
	    xmlChar *val;

            /*  如果需要则规范化 */
	    val = xmlExpandEntitiesInAttValue(ctxt, value, /* normalize */ 0);

	    if (val == NULL)
		ctxt->valid &= xmlValidateOneAttribute(&ctxt->vctxt,
				ctxt->myDoc, ctxt->node, ret, value);
	    else {
		xmlChar *nvalnorm;

		/*
		 * 执行属性规范化的最后阶段
		 * 需要执行两次...这是与在属性中保持xmlSAX2References能力相关的额外负担
		 */
                nvalnorm = xmlValidCtxtNormalizeAttributeValue(
                                 &ctxt->vctxt, ctxt->myDoc,
                                 ctxt->node, fullname, val);
		if (nvalnorm != NULL) {
		    xmlFree(val);
		    val = nvalnorm;
		}

		ctxt->valid &= xmlValidateOneAttribute(&ctxt->vctxt,
			        ctxt->myDoc, ctxt->node, ret, val);
                xmlFree(val);
	    }
	} else {
	    ctxt->valid &= xmlValidateOneAttribute(&ctxt->vctxt, ctxt->myDoc,
					       ctxt->node, ret, value);
	}
    } else
#endif /* LIBXML_VALID_ENABLED */
           if (((ctxt->loadsubset & XML_SKIP_IDS) == 0) &&
               (ctxt->input->entity == NULL) &&
               /* Don't create IDs containing entity references */
               (ret->children != NULL) &&
               (ret->children->type == XML_TEXT_NODE) &&
               (ret->children->next == NULL)) {
        xmlChar *content = ret->children->content;
        /*
	 * when validating, the ID registration is done at the attribute
	 * validation level. Otherwise we have to do specific handling here.
	 */
	if (xmlStrEqual(fullname, BAD_CAST "xml:id")) {
	    /*
	     * Add the xml:id value
	     *
	     * Open issue: normalization of the value.
	     */
	    if (xmlValidateNCName(content, 1) != 0) {
	        xmlErrValid(ctxt, XML_DTD_XMLID_VALUE,
		            "xml:id : attribute value %s is not an NCName\n",
		            content, NULL);
	    }
	    xmlAddID(&ctxt->vctxt, ctxt->myDoc, content, ret);
	} else {
            int res = xmlIsID(ctxt->myDoc, ctxt->node, ret);

            if (res < 0)
                xmlCtxtErrMemory(ctxt);
            else if (res > 0)
                xmlAddID(&ctxt->vctxt, ctxt->myDoc, content, ret);
            else if (xmlIsRef(ctxt->myDoc, ctxt->node, ret))
                xmlAddRef(&ctxt->vctxt, ctxt->myDoc, content, ret);
        }
    }

error:
    if (ns != NULL)
	xmlFree(ns);
}

/*
 *
 * 检查DTD中的默认属性
 *
 * 已弃用的SAX1接口。
 */
static void
xmlCheckDefaultedAttributes(xmlParserCtxtPtr ctxt, const xmlChar *name,
	const xmlChar *prefix, const xmlChar **atts) {
    xmlElementPtr elemDecl;
    const xmlChar *att;
    int internal = 1;
    int i;

    elemDecl = xmlGetDtdQElementDesc(ctxt->myDoc->intSubset, name, prefix);
    if (elemDecl == NULL) {
	elemDecl = xmlGetDtdQElementDesc(ctxt->myDoc->extSubset, name, prefix);
	internal = 0;
    }

process_external_subset:

    if (elemDecl != NULL) {
	xmlAttributePtr attr = elemDecl->attributes;
	/*
	 * 如果文档标记为独立，则检查来自外部子集的默认属性
	 */
	if ((ctxt->myDoc->standalone == 1) &&
	    (ctxt->myDoc->extSubset != NULL) &&
	    (ctxt->validate)) {
	    while (attr != NULL) {
		if ((attr->defaultValue != NULL) &&
		    (xmlGetDtdQAttrDesc(ctxt->myDoc->extSubset,
					attr->elem, attr->name,
					attr->prefix) == attr) &&
		    (xmlGetDtdQAttrDesc(ctxt->myDoc->intSubset,
					attr->elem, attr->name,
					attr->prefix) == NULL)) {
		    xmlChar *fulln;

		    if (attr->prefix != NULL) {
			fulln = xmlStrdup(attr->prefix);
                        if (fulln != NULL)
			    fulln = xmlStrcat(fulln, BAD_CAST ":");
                        if (fulln != NULL)
			    fulln = xmlStrcat(fulln, attr->name);
		    } else {
			fulln = xmlStrdup(attr->name);
		    }
                    if (fulln == NULL) {
                        xmlSAX2ErrMemory(ctxt);
                        break;
                    }

		    /*
		     * 检查属性是否未在序列化中声明
		     */
		    att = NULL;
		    if (atts != NULL) {
			i = 0;
			att = atts[i];
			while (att != NULL) {
			    if (xmlStrEqual(att, fulln))
				break;
			    i += 2;
			    att = atts[i];
			}
		    }
		    if (att == NULL) {
		        xmlErrValid(ctxt, XML_DTD_STANDALONE_DEFAULTED,
      "standalone: attribute %s on %s defaulted from external subset\n",
				    fulln,
				    attr->elem);
		    }
                    xmlFree(fulln);
		}
		attr = attr->nexth;
	    }
	}

	/*
	 * 在需要时实际插入默认值
	 */
	attr = elemDecl->attributes;
	while (attr != NULL) {
	    /*
	     * 确保内部子集中发生的属性重定义不会被外部子集中的定义覆盖。
	     */
	    if (attr->defaultValue != NULL) {
		/*
		 * 在以下情况下元素应在树中实例化：
		 *  - 这是一个命名空间前缀
		 *  - 用户需要在树中完成，如XSLT
		 *  - 内部子集中还没有覆盖它的属性定义。
		 */
		if (((attr->prefix != NULL) &&
		     (xmlStrEqual(attr->prefix, BAD_CAST "xmlns"))) ||
		    ((attr->prefix == NULL) &&
		     (xmlStrEqual(attr->name, BAD_CAST "xmlns"))) ||
		    (ctxt->loadsubset & XML_COMPLETE_ATTRS)) {
		    xmlAttributePtr tst;

		    tst = xmlGetDtdQAttrDesc(ctxt->myDoc->intSubset,
					     attr->elem, attr->name,
					     attr->prefix);
		    if ((tst == attr) || (tst == NULL)) {
		        xmlChar fn[50];
			xmlChar *fulln;

                        fulln = xmlBuildQName(attr->name, attr->prefix, fn, 50);
			if (fulln == NULL) {
			    xmlSAX2ErrMemory(ctxt);
			    return;
			}

			/*
			 * 检查属性是否未在序列化中声明
			 */
			att = NULL;
			if (atts != NULL) {
			    i = 0;
			    att = atts[i];
			    while (att != NULL) {
				if (xmlStrEqual(att, fulln))
				    break;
				i += 2;
				att = atts[i];
			    }
			}
			if (att == NULL) {
			    xmlSAX1Attribute(ctxt, fulln,
					     attr->defaultValue, prefix);
			}
			if ((fulln != fn) && (fulln != attr->name))
			    xmlFree(fulln);
		    }
		}
	    }
	    attr = attr->nexth;
	}
	if (internal == 1) {
	    elemDecl = xmlGetDtdQElementDesc(ctxt->myDoc->extSubset,
		                             name, prefix);
	    internal = 0;
	    goto process_external_subset;
	}
    }
}

/**
 * 处理开始标签时调用。
 *
 * 已弃用的SAX1接口。
 * @param ctx  用户数据（XML解析器上下文）
 * @param fullname  元素名称，包括命名空间前缀
 * @param atts  名称/值属性对数组，以NULL结尾
 */
static void
xmlSAX1StartElement(void *ctx, const xmlChar *fullname, const xmlChar **atts)
{
    xmlParserCtxtPtr ctxt = (xmlParserCtxtPtr) ctx;
    xmlNodePtr ret;
    xmlNodePtr parent;
    xmlNsPtr ns;
    const xmlChar *name;
    xmlChar *prefix;
    const xmlChar *att;
    const xmlChar *value;
    int i, res;

    if ((ctx == NULL) || (fullname == NULL) || (ctxt->myDoc == NULL)) return;

    /*
     * First check on validity:
     */
    if (ctxt->validate && (ctxt->myDoc->extSubset == NULL) &&
        ((ctxt->myDoc->intSubset == NULL) ||
	 ((ctxt->myDoc->intSubset->notations == NULL) &&
	  (ctxt->myDoc->intSubset->elements == NULL) &&
	  (ctxt->myDoc->intSubset->attributes == NULL) &&
	  (ctxt->myDoc->intSubset->entities == NULL)))) {
	xmlErrValid(ctxt, XML_ERR_NO_DTD,
	  "Validation failed: no DTD found !", NULL, NULL);
	ctxt->validate = 0;
    }

    /*
     * Split the full name into a namespace prefix and the tag name
     */
    name = xmlSplitQName4(fullname, &prefix);
    if (name == NULL) {
        xmlSAX2ErrMemory(ctxt);
        return;
    }

    /*
     * Note : the namespace resolution is deferred until the end of the
     *        attributes parsing, since local namespace can be defined as
     *        an attribute at this level.
     */
    ret = xmlNewDocNode(ctxt->myDoc, NULL, name, NULL);
    if (ret == NULL) {
	xmlFree(prefix);
	xmlSAX2ErrMemory(ctxt);
        return;
    }
    ctxt->nodemem = -1;

    /* Initialize parent before pushing node */
    parent = ctxt->node;
    if (parent == NULL)
        parent = (xmlNodePtr) ctxt->myDoc;

    /*
     * Link the child element
     */
    xmlSAX2AppendChild(ctxt, ret);

    /*
     * We are parsing a new node.
     */
    if (nodePush(ctxt, ret) < 0) {
        xmlUnlinkNode(ret);
        xmlFreeNode(ret);
        if (prefix != NULL)
            xmlFree(prefix);
        return;
    }

    /*
     * Insert all the defaulted attributes from the DTD especially
     * namespaces
     */
    if ((ctxt->myDoc->intSubset != NULL) ||
        (ctxt->myDoc->extSubset != NULL)) {
        xmlCheckDefaultedAttributes(ctxt, name, prefix, atts);
    }

    /*
     * process all the attributes whose name start with "xmlns"
     */
    if (atts != NULL) {
        i = 0;
        att = atts[i++];
        value = atts[i++];
        while ((att != NULL) && (value != NULL)) {
            if ((att[0] == 'x') && (att[1] == 'm') && (att[2] == 'l') &&
                (att[3] == 'n') && (att[4] == 's'))
                xmlSAX1Attribute(ctxt, att, value, prefix);

            att = atts[i++];
            value = atts[i++];
        }
    }

    /*
     * Search the namespace, note that since the attributes have been
     * processed, the local namespaces are available.
     */
    res = xmlSearchNsSafe(ret, prefix, &ns);
    if (res < 0)
        xmlSAX2ErrMemory(ctxt);
    if ((ns == NULL) && (parent != NULL)) {
        res = xmlSearchNsSafe(parent, prefix, &ns);
        if (res < 0)
            xmlSAX2ErrMemory(ctxt);
    }
    if ((prefix != NULL) && (ns == NULL)) {
        xmlNsWarnMsg(ctxt, XML_NS_ERR_UNDEFINED_NAMESPACE,
                     "Namespace prefix %s is not defined\n",
                     prefix, NULL);
        ns = xmlNewNs(ret, NULL, prefix);
        if (ns == NULL)
            xmlSAX2ErrMemory(ctxt);
    }

    /*
     * set the namespace node, making sure that if the default namespace
     * is unbound on a parent we simply keep it NULL
     */
    if ((ns != NULL) && (ns->href != NULL) &&
        ((ns->href[0] != 0) || (ns->prefix != NULL)))
        xmlSetNs(ret, ns);

    /*
     * process all the other attributes
     */
    if (atts != NULL) {
        i = 0;
	att = atts[i++];
	value = atts[i++];
        while ((att != NULL) && (value != NULL)) {
            if ((att[0] != 'x') || (att[1] != 'm') || (att[2] != 'l') ||
                (att[3] != 'n') || (att[4] != 's'))
                xmlSAX1Attribute(ctxt, att, value, NULL);

            /*
             * Next ones
             */
            att = atts[i++];
            value = atts[i++];
        }
    }

#ifdef LIBXML_VALID_ENABLED
    /*
     * If it's the Document root, finish the DTD validation and
     * check the document root element for validity
     */
    if ((ctxt->validate) &&
        ((ctxt->vctxt.flags & XML_VCTXT_DTD_VALIDATED) == 0)) {
	int chk;

	chk = xmlValidateDtdFinal(&ctxt->vctxt, ctxt->myDoc);
	if (chk <= 0)
	    ctxt->valid = 0;
	if (chk < 0)
	    ctxt->wellFormed = 0;
	ctxt->valid &= xmlValidateRoot(&ctxt->vctxt, ctxt->myDoc);
	ctxt->vctxt.flags |= XML_VCTXT_DTD_VALIDATED;
    }
#endif /* LIBXML_VALID_ENABLED */

    if (prefix != NULL)
	xmlFree(prefix);

}
#endif /* LIBXML_SAX1_ENABLED */

#ifdef LIBXML_HTML_ENABLED
static void
xmlSAX2HtmlAttribute(xmlParserCtxtPtr ctxt, const xmlChar *fullname,
                     const xmlChar *value) {
    xmlAttrPtr ret;
    xmlChar *nval = NULL;

    ret = xmlNewNsProp(ctxt->node, NULL, fullname, NULL);
    if (ret == NULL) {
        xmlSAX2ErrMemory(ctxt);
        return;
    }

    if ((value == NULL) && (htmlIsBooleanAttr(fullname))) {
        nval = xmlStrdup(fullname);
        if (nval == NULL) {
            xmlSAX2ErrMemory(ctxt);
            return;
        }
        value = nval;
    }

    if (value != NULL) {
        ret->children = xmlNewDocText(ctxt->myDoc, value);
        if (ret->children == NULL) {
            xmlSAX2ErrMemory(ctxt);
        } else {
            ret->last = ret->children;
            ret->children->parent = (xmlNodePtr) ret;
        }
    }

    if (((ctxt->loadsubset & XML_SKIP_IDS) == 0) &&
        /*
         * Don't create IDs containing entity references (should
         * be always the case with HTML)
         */
        (ret->children != NULL) &&
        (ret->children->type == XML_TEXT_NODE) &&
        (ret->children->next == NULL)) {
        int res = xmlIsID(ctxt->myDoc, ctxt->node, ret);

        if (res < 0)
            xmlCtxtErrMemory(ctxt);
        else if (res > 0)
            xmlAddID(&ctxt->vctxt, ctxt->myDoc, ret->children->content, ret);
    }

    if (nval != NULL)
        xmlFree(nval);
}

/**
 * Called when an opening tag has been processed.
 *
 * @param ctxt  parser context
 * @param fullname  The element name, including namespace prefix
 * @param atts  An array of name/value attributes pairs, NULL terminated
 */
static void
xmlSAX2StartHtmlElement(xmlParserCtxtPtr ctxt, const xmlChar *fullname,
                        const xmlChar **atts) {
    xmlNodePtr ret;
    xmlNodePtr parent;
    const xmlChar *att;
    const xmlChar *value;
    int i;

    ret = xmlNewDocNode(ctxt->myDoc, NULL, fullname, NULL);
    if (ret == NULL) {
	xmlSAX2ErrMemory(ctxt);
        return;
    }
    ctxt->nodemem = -1;

    /* Initialize parent before pushing node */
    parent = ctxt->node;
    if (parent == NULL)
        parent = (xmlNodePtr) ctxt->myDoc;

    /*
     * Link the child element
     */
    xmlSAX2AppendChild(ctxt, ret);

    /*
     * We are parsing a new node.
     */
    if (nodePush(ctxt, ret) < 0) {
        xmlUnlinkNode(ret);
        xmlFreeNode(ret);
        return;
    }

    if (atts != NULL) {
        i = 0;
	att = atts[i++];
	value = atts[i++];
        while (att != NULL) {
            xmlSAX2HtmlAttribute(ctxt, att, value);
            att = atts[i++];
            value = atts[i++];
        }
    }
}
#endif /* LIBXML_HTML_ENABLED */

/**
 * Called when an opening tag has been processed.
 *
 * @deprecated Don't call this function directly.
 *
 * Used for HTML and SAX1.
 * @param ctx  the user data (XML parser context)
 * @param fullname  The element name, including namespace prefix
 * @param atts  An array of name/value attributes pairs, NULL terminated
 */
void
xmlSAX2StartElement(void *ctx, const xmlChar *fullname, const xmlChar **atts) {
    xmlParserCtxtPtr ctxt = (xmlParserCtxtPtr) ctx;

    (void) atts;

    if ((ctxt == NULL) || (fullname == NULL) || (ctxt->myDoc == NULL))
        return;

#ifdef LIBXML_SAX1_ENABLED
    if (!ctxt->html) {
        xmlSAX1StartElement(ctxt, fullname, atts);
        return;
    }
#endif

#ifdef LIBXML_HTML_ENABLED
    if (ctxt->html) {
        xmlSAX2StartHtmlElement(ctxt, fullname, atts);
        return;
    }
#endif
}

/**
 * called when the end of an element has been detected.
 *
 * @deprecated Don't call this function directly.
 *
 * Used for HTML and SAX1.
 * @param ctx  the user data (XML parser context)
 * @param name  The element name
 */
void
xmlSAX2EndElement(void *ctx, const xmlChar *name ATTRIBUTE_UNUSED)
{
    xmlParserCtxtPtr ctxt = (xmlParserCtxtPtr) ctx;

    if (ctxt == NULL)
        return;

#if defined(LIBXML_SAX1_ENABLED) && defined(LIBXML_VALID_ENABLED)
    if (!ctxt->html && ctxt->validate && ctxt->wellFormed &&
        ctxt->myDoc && ctxt->myDoc->intSubset)
        ctxt->valid &= xmlValidateOneElement(&ctxt->vctxt, ctxt->myDoc,
					     ctxt->node);
#endif /* LIBXML_VALID_ENABLED */

#if defined(LIBXML_SAX1_ENABLED) || defined(LIBXML_HTML_ENABLED)
    ctxt->nodemem = -1;

    /*
     * end of parsing of this node.
     */
    nodePop(ctxt);
#endif
}

/*
 * @param ctxt  解析器上下文
 * @param str  输入字符串
 * @param len  字符串长度
 *
 * 文本节点的回调
 *
 * @returns 新分配的字符串，如果不需要或出错则返回NULL
 */
static xmlNodePtr
xmlSAX2TextNode(xmlParserCtxtPtr ctxt, const xmlChar *str, int len) {
    xmlNodePtr ret;
    const xmlChar *intern = NULL;

    /*
     * 分配
     */
    if (ctxt->freeElems != NULL) {
	ret = ctxt->freeElems;
	ctxt->freeElems = ret->next;
	ctxt->freeElemsNr--;
    } else {
	ret = (xmlNodePtr) xmlMalloc(sizeof(xmlNode));
    }
    if (ret == NULL) {
        xmlCtxtErrMemory(ctxt);
	return(NULL);
    }
    memset(ret, 0, sizeof(xmlNode));
    /*
     * 内化在标签之间找到的格式化空白，或非常短的字符串
     */
    if ((!ctxt->html) && (ctxt->dictNames)) {
        xmlChar cur = str[len];

	if ((len < (int) (2 * sizeof(void *))) &&
	    (ctxt->options & XML_PARSE_COMPACT)) {
	    /* 在节点中存储字符串，覆盖properties和nsDef */
	    xmlChar *tmp = (xmlChar *) &(ret->properties);
	    memcpy(tmp, str, len);
	    tmp[len] = 0;
	    intern = tmp;
	} else if ((len <= 3) && ((cur == '"') || (cur == '\'') ||
	    ((cur == '<') && (str[len + 1] != '!')))) {
	    intern = xmlDictLookup(ctxt->dict, str, len);
            if (intern == NULL) {
                xmlSAX2ErrMemory(ctxt);
                xmlFree(ret);
                return(NULL);
            }
	} else if (IS_BLANK_CH(*str) && (len < 60) && (cur == '<') &&
	           (str[len + 1] != '!')) {
	    int i;

	    for (i = 1;i < len;i++) {
		if (!IS_BLANK_CH(str[i])) goto skip;
	    }
	    intern = xmlDictLookup(ctxt->dict, str, len);
            if (intern == NULL) {
                xmlSAX2ErrMemory(ctxt);
                xmlFree(ret);
                return(NULL);
            }
	}
    }
skip:
    ret->type = XML_TEXT_NODE;

    ret->name = xmlStringText;
    if (intern == NULL) {
	ret->content = xmlStrndup(str, len);
	if (ret->content == NULL) {
	    xmlSAX2ErrMemory(ctxt);
	    xmlFree(ret);
	    return(NULL);
	}
    } else
	ret->content = (xmlChar *) intern;

    if ((xmlRegisterCallbacks) && (xmlRegisterNodeDefaultValue))
	xmlRegisterNodeDefaultValue(ret);
    return(ret);
}

#ifdef LIBXML_VALID_ENABLED
/*
 * @param ctxt  解析器上下文
 * @param str  输入字符串
 * @param len  字符串长度
 *
 * 从属性值中移除实体
 *
 * @returns 新分配的字符串，如果不需要或出错则返回NULL
 */
static xmlChar *
xmlSAX2DecodeAttrEntities(xmlParserCtxtPtr ctxt, const xmlChar *str,
                          const xmlChar *end) {
    const xmlChar *in;

    in = str;
    while (in < end)
        if (*in++ == '&')
	    goto decode;
    return(NULL);
decode:
    /*
     * 如果值包含'&'，我们可以确定它已被分配并以零结尾。
     */
    /*  如果需要则规范化 */
    return(xmlExpandEntitiesInAttValue(ctxt, str, /* normalize */ 0));
}
#endif /* LIBXML_VALID_ENABLED */

/**
 * 处理解析器读取的属性。
 * 默认处理是将属性转换为DOM子树，并将其粘贴到添加到元素的新xmlAttr元素中。
 *
 * @param ctxt  解析器上下文
 * @param localname  属性的本地名称
 * @param prefix  属性命名空间前缀（如果可用）
 * @param value  属性值的开始
 * @param valueend  属性值的结束
 * @returns 新属性，如果出错则返回NULL。
 */
static xmlAttrPtr
xmlSAX2AttributeNs(xmlParserCtxtPtr ctxt,
                   const xmlChar * localname,
                   const xmlChar * prefix,
		   const xmlChar * value,
		   const xmlChar * valueend)
{
    xmlAttrPtr ret;
    xmlNsPtr namespace = NULL;
    xmlChar *dup = NULL;

    /*
     * 注意：如果prefix == NULL，属性不在默认命名空间中
     */
    if (prefix != NULL) {
	namespace = xmlParserNsLookupSax(ctxt, prefix);
	if ((namespace == NULL) && (xmlStrEqual(prefix, BAD_CAST "xml"))) {
            int res;

	    res = xmlSearchNsSafe(ctxt->node, prefix, &namespace);
            if (res < 0)
                xmlSAX2ErrMemory(ctxt);
	}
    }

    /*
     * 分配节点
     */
    if (ctxt->freeAttrs != NULL) {
        ret = ctxt->freeAttrs;
	ctxt->freeAttrs = ret->next;
	ctxt->freeAttrsNr--;
    } else {
        ret = xmlMalloc(sizeof(*ret));
        if (ret == NULL) {
            xmlSAX2ErrMemory(ctxt);
            return(NULL);
        }
    }

    memset(ret, 0, sizeof(xmlAttr));
    ret->type = XML_ATTRIBUTE_NODE;

    /*
     * xmlParseBalancedChunkMemoryRecover有一个错误，可能导致
     * ctxt->node->doc和ctxt->myDoc之间不匹配。我们在这里使用
     * ctxt->node->doc，但我们应该以某种方式确保文档指针匹配。
     */

    /* assert(ctxt->node->doc == ctxt->myDoc); */

    ret->parent = ctxt->node;
    ret->doc = ctxt->node->doc;
    ret->ns = namespace;

    if (ctxt->dictNames) {
        ret->name = localname;
    } else {
        ret->name = xmlStrdup(localname);
        if (ret->name == NULL)
            xmlSAX2ErrMemory(ctxt);
    }

    if ((xmlRegisterCallbacks) && (xmlRegisterNodeDefaultValue))
        xmlRegisterNodeDefaultValue((xmlNodePtr)ret);

    if ((ctxt->replaceEntities == 0) && (!ctxt->html)) {
	xmlNodePtr tmp;

	/*
	 * 我们知道如果有实体引用，那么字符串已被复制并以0结尾，
	 * 否则以'或"结尾
	 */
	if (*valueend != 0) {
	    tmp = xmlSAX2TextNode(ctxt, value, valueend - value);
	    ret->children = tmp;
	    ret->last = tmp;
	    if (tmp != NULL) {
		tmp->doc = ret->doc;
		tmp->parent = (xmlNodePtr) ret;
	    }
	} else if (valueend > value) {
            if (xmlNodeParseContent((xmlNodePtr) ret, value,
                                    valueend - value) < 0)
                xmlSAX2ErrMemory(ctxt);
	}
    } else if (value != NULL) {
	xmlNodePtr tmp;

	tmp = xmlSAX2TextNode(ctxt, value, valueend - value);
	ret->children = tmp;
	ret->last = tmp;
	if (tmp != NULL) {
	    tmp->doc = ret->doc;
	    tmp->parent = (xmlNodePtr) ret;
	}
    }

#ifdef LIBXML_VALID_ENABLED
    if ((!ctxt->html) && ctxt->validate && ctxt->wellFormed &&
        ctxt->myDoc && ctxt->myDoc->intSubset) {
	/*
	 * If we don't substitute entities, the validation should be
	 * done on a value with replaced entities anyway.
	 */
        if (!ctxt->replaceEntities) {
	    dup = xmlSAX2DecodeAttrEntities(ctxt, value, valueend);
	    if (dup == NULL) {
	        if (*valueend == 0) {
		    ctxt->valid &= xmlValidateOneAttribute(&ctxt->vctxt,
				    ctxt->myDoc, ctxt->node, ret, value);
		} else {
		    /*
		     * 那应该已经被规范化了。
		     * 在这里最终分配比在完整验证代码中重复入口点更便宜
		     */
		    dup = xmlStrndup(value, valueend - value);
                    if (dup == NULL)
                        xmlSAX2ErrMemory(ctxt);

		    ctxt->valid &= xmlValidateOneAttribute(&ctxt->vctxt,
				    ctxt->myDoc, ctxt->node, ret, dup);
		}
	    } else {
	        /*
		 * dup现在包含已替换实体的扁平化属性内容字符串。
		 * 检查是否需要应用额外的规范化层。
		 * 需要执行两次...这是与在属性中保持引用能力相关的额外负担
		 */
		if (ctxt->attsSpecial != NULL) {
		    xmlChar *nvalnorm;
		    xmlChar fn[50];
		    xmlChar *fullname;

		    fullname = xmlBuildQName(localname, prefix, fn, 50);
                    if (fullname == NULL) {
                        xmlSAX2ErrMemory(ctxt);
                    } else {
			ctxt->vctxt.valid = 1;
		        nvalnorm = xmlValidCtxtNormalizeAttributeValue(
			                 &ctxt->vctxt, ctxt->myDoc,
					 ctxt->node, fullname, dup);
			if (ctxt->vctxt.valid != 1)
			    ctxt->valid = 0;

			if ((fullname != fn) && (fullname != localname))
			    xmlFree(fullname);
			if (nvalnorm != NULL) {
			    xmlFree(dup);
			    dup = nvalnorm;
			}
		    }
		}

		ctxt->valid &= xmlValidateOneAttribute(&ctxt->vctxt,
			        ctxt->myDoc, ctxt->node, ret, dup);
	    }
	} else {
	    /*
	     * 如果实体已经被替换，那么传递的属性已经被规范化
	     */
	    dup = xmlStrndup(value, valueend - value);
            if (dup == NULL)
                xmlSAX2ErrMemory(ctxt);

	    ctxt->valid &= xmlValidateOneAttribute(&ctxt->vctxt,
	                             ctxt->myDoc, ctxt->node, ret, dup);
	}
    } else
#endif /* LIBXML_VALID_ENABLED */
           if (((ctxt->loadsubset & XML_SKIP_IDS) == 0) &&
               (ctxt->input->entity == NULL) &&
               /* 不要创建包含实体引用的ID */
               (ret->children != NULL) &&
               (ret->children->type == XML_TEXT_NODE) &&
               (ret->children->next == NULL)) {
        xmlChar *content = ret->children->content;
        /*
	 * 验证时，ID注册在属性验证级别完成。否则我们必须在这里进行特定处理。
	 */
        if ((prefix == ctxt->str_xml) &&
	           (localname[0] == 'i') && (localname[1] == 'd') &&
		   (localname[2] == 0)) {
	    /*
	     * 添加xml:id值
	     *
	     * 未解决问题：值的规范化。
	     */
	    if (xmlValidateNCName(content, 1) != 0) {
	        xmlErrValid(ctxt, XML_DTD_XMLID_VALUE,
                            "xml:id : attribute value %s is not an NCName\n",
                            content, NULL);
	    }
	    xmlAddID(&ctxt->vctxt, ctxt->myDoc, content, ret);
	} else {
            int res = xmlIsID(ctxt->myDoc, ctxt->node, ret);

            if (res < 0)
                xmlCtxtErrMemory(ctxt);
            else if (res > 0)
                xmlAddID(&ctxt->vctxt, ctxt->myDoc, content, ret);
            else if (xmlIsRef(ctxt->myDoc, ctxt->node, ret))
                xmlAddRef(&ctxt->vctxt, ctxt->myDoc, content, ret);
	}
    }
    if (dup != NULL)
	xmlFree(dup);

    return(ret);
}

/**
 * 解析器检测到元素开始时的SAX2回调。
 * 它提供元素的命名空间信息，以及元素上的新命名空间声明。
 *
 * @param ctx  用户数据（XML解析器上下文）
 * @param localname  元素的本地名称
 * @param prefix  元素命名空间前缀（如果可用）
 * @param URI  元素命名空间名称（如果可用）
 * @param nb_namespaces  该节点上的命名空间定义数量
 * @param namespaces  指向前缀/URI对命名空间定义数组的指针
 * @param nb_attributes  该节点上的属性数量
 * @param nb_defaulted  默认属性的数量。
 * @param attributes  指向（localname/prefix/URI/value/end）属性值数组的指针。
 */
void
xmlSAX2StartElementNs(void *ctx,
                      const xmlChar *localname,
		      const xmlChar *prefix,
		      const xmlChar *URI,
		      int nb_namespaces,
		      const xmlChar **namespaces,
		      int nb_attributes,
		      int nb_defaulted,
		      const xmlChar **attributes)
{
    xmlParserCtxtPtr ctxt = (xmlParserCtxtPtr) ctx;
    xmlNodePtr ret;
    xmlNsPtr last = NULL, ns;
    const xmlChar *uri, *pref;
    xmlChar *lname = NULL;
    int i, j;

    if (ctx == NULL) return;
    /*
     * 首先检查有效性：
     */
    if (ctxt->validate &&
        ((ctxt->myDoc == NULL) ||
         ((ctxt->myDoc->extSubset == NULL) &&
          ((ctxt->myDoc->intSubset == NULL) ||
	   ((ctxt->myDoc->intSubset->notations == NULL) &&
	    (ctxt->myDoc->intSubset->elements == NULL) &&
	    (ctxt->myDoc->intSubset->attributes == NULL) &&
	    (ctxt->myDoc->intSubset->entities == NULL)))))) {
	xmlErrValid(ctxt, XML_DTD_NO_DTD,
	  "Validation failed: no DTD found !", NULL, NULL);
	ctxt->validate = 0;
    }

    /*
     * 处理未定义命名空间前缀的罕见情况
     */
    if ((prefix != NULL) && (URI == NULL)) {
        if (ctxt->dictNames) {
	    const xmlChar *fullname;

	    fullname = xmlDictQLookup(ctxt->dict, prefix, localname);
	    if (fullname == NULL) {
                xmlSAX2ErrMemory(ctxt);
                return;
            }
	    localname = fullname;
	} else {
	    lname = xmlBuildQName(localname, prefix, NULL, 0);
            if (lname == NULL) {
                xmlSAX2ErrMemory(ctxt);
                return;
            }
	}
    }
    /*
     * 分配节点
     */
    if (ctxt->freeElems != NULL) {
        ret = ctxt->freeElems;
	ctxt->freeElems = ret->next;
	ctxt->freeElemsNr--;
	memset(ret, 0, sizeof(xmlNode));
        ret->doc = ctxt->myDoc;
	ret->type = XML_ELEMENT_NODE;

	if (ctxt->dictNames)
	    ret->name = localname;
	else {
	    if (lname == NULL)
		ret->name = xmlStrdup(localname);
	    else
	        ret->name = lname;
	    if (ret->name == NULL) {
	        xmlSAX2ErrMemory(ctxt);
                xmlFree(ret);
		return;
	    }
	}
	if ((xmlRegisterCallbacks) && (xmlRegisterNodeDefaultValue))
	    xmlRegisterNodeDefaultValue(ret);
    } else {
	if (ctxt->dictNames)
	    ret = xmlNewDocNodeEatName(ctxt->myDoc, NULL,
	                               (xmlChar *) localname, NULL);
	else if (lname == NULL)
	    ret = xmlNewDocNode(ctxt->myDoc, NULL, localname, NULL);
	else
	    ret = xmlNewDocNodeEatName(ctxt->myDoc, NULL, lname, NULL);
	if (ret == NULL) {
	    xmlSAX2ErrMemory(ctxt);
	    return;
	}
    }

    /*
     * 构建命名空间列表
     */
    for (i = 0,j = 0;j < nb_namespaces;j++) {
        pref = namespaces[i++];
	uri = namespaces[i++];
	ns = xmlNewNs(NULL, uri, pref);
	if (ns != NULL) {
	    if (last == NULL) {
	        ret->nsDef = last = ns;
	    } else {
	        last->next = ns;
		last = ns;
	    }
	    if ((URI != NULL) && (prefix == pref))
		ret->ns = ns;
	} else {
            xmlSAX2ErrMemory(ctxt);
	    continue;
	}

        xmlParserNsUpdateSax(ctxt, pref, ns);

#ifdef LIBXML_VALID_ENABLED
	if ((!ctxt->html) && ctxt->validate && ctxt->wellFormed &&
	    ctxt->myDoc && ctxt->myDoc->intSubset) {
	    ctxt->valid &= xmlValidateOneNamespace(&ctxt->vctxt, ctxt->myDoc,
	                                           ret, prefix, ns, uri);
	}
#endif /* LIBXML_VALID_ENABLED */
    }
    ctxt->nodemem = -1;

    /*
     * 链接子元素
     */
    xmlSAX2AppendChild(ctxt, ret);

    /*
     * 我们正在解析一个新节点。
     */
    if (nodePush(ctxt, ret) < 0) {
        xmlUnlinkNode(ret);
        xmlFreeNode(ret);
        return;
    }

    /*
     * 仅在请求时从DTD插入默认属性：
     */
    if ((nb_defaulted != 0) &&
        ((ctxt->loadsubset & XML_COMPLETE_ATTRS) == 0))
	nb_attributes -= nb_defaulted;

    /*
     * 如果尚未找到命名空间则搜索
     * 注意，如果prefix为NULL，这将搜索默认命名空间
     */
    if ((URI != NULL) && (ret->ns == NULL)) {
        ret->ns = xmlParserNsLookupSax(ctxt, prefix);
	if ((ret->ns == NULL) && (xmlStrEqual(prefix, BAD_CAST "xml"))) {
            int res;

	    res = xmlSearchNsSafe(ret, prefix, &ret->ns);
            if (res < 0)
                xmlSAX2ErrMemory(ctxt);
	}
	if (ret->ns == NULL) {
	    ns = xmlNewNs(ret, NULL, prefix);
	    if (ns == NULL) {

	        xmlSAX2ErrMemory(ctxt);
		return;
	    }
            if (prefix != NULL)
                xmlNsWarnMsg(ctxt, XML_NS_ERR_UNDEFINED_NAMESPACE,
                             "Namespace prefix %s was not found\n",
                             prefix, NULL);
            else
                xmlNsWarnMsg(ctxt, XML_NS_ERR_UNDEFINED_NAMESPACE,
                             "Namespace default prefix was not found\n",
                             NULL, NULL);
	}
    }

    /*
     * 处理所有其他属性
     */
    if (nb_attributes > 0) {
        xmlAttrPtr prev = NULL;

        for (j = 0,i = 0;i < nb_attributes;i++,j+=5) {
            xmlAttrPtr attr = NULL;

	    /*
	     * 处理未定义属性前缀的罕见情况
	     */
	    if ((attributes[j+1] != NULL) && (attributes[j+2] == NULL)) {
		if (ctxt->dictNames) {
		    const xmlChar *fullname;

		    fullname = xmlDictQLookup(ctxt->dict, attributes[j+1],
		                              attributes[j]);
		    if (fullname == NULL) {
                        xmlSAX2ErrMemory(ctxt);
                        return;
                    }
                    attr = xmlSAX2AttributeNs(ctxt, fullname, NULL,
                                              attributes[j+3],
                                              attributes[j+4]);
                    goto have_attr;
		} else {
		    lname = xmlBuildQName(attributes[j], attributes[j+1],
		                          NULL, 0);
		    if (lname == NULL) {
                        xmlSAX2ErrMemory(ctxt);
                        return;
                    }
                    attr = xmlSAX2AttributeNs(ctxt, lname, NULL,
                                              attributes[j+3],
                                              attributes[j+4]);
                    xmlFree(lname);
                    goto have_attr;
		}
	    }
            attr = xmlSAX2AttributeNs(ctxt, attributes[j], attributes[j+1],
                                      attributes[j+3], attributes[j+4]);
have_attr:
            if (attr == NULL)
                continue;

            /* 在末尾链接以保持顺序 */
            if (prev == NULL) {
                ctxt->node->properties = attr;
            } else {
                prev->next = attr;
                attr->prev = prev;
            }

            prev = attr;
	}
    }

#ifdef LIBXML_VALID_ENABLED
    /*
     * 如果是文档根，完成DTD验证并检查文档根元素的有效性
     */
    if ((ctxt->validate) &&
        ((ctxt->vctxt.flags & XML_VCTXT_DTD_VALIDATED) == 0)) {
	int chk;

	chk = xmlValidateDtdFinal(&ctxt->vctxt, ctxt->myDoc);
	if (chk <= 0)
	    ctxt->valid = 0;
	if (chk < 0)
	    ctxt->wellFormed = 0;
	ctxt->valid &= xmlValidateRoot(&ctxt->vctxt, ctxt->myDoc);
	ctxt->vctxt.flags |= XML_VCTXT_DTD_VALIDATED;
    }
#endif /* LIBXML_VALID_ENABLED */
}

/**
 * 解析器检测到元素结束时的SAX2回调。
 * 它提供元素的命名空间信息。
 *
 * @param ctx  用户数据（XML解析器上下文）
 * @param localname  元素的本地名称
 * @param prefix  元素命名空间前缀（如果可用）
 * @param URI  元素命名空间名称（如果可用）
 */
void
xmlSAX2EndElementNs(void *ctx,
                    const xmlChar * localname ATTRIBUTE_UNUSED,
                    const xmlChar * prefix ATTRIBUTE_UNUSED,
		    const xmlChar * URI ATTRIBUTE_UNUSED)
{
    xmlParserCtxtPtr ctxt = (xmlParserCtxtPtr) ctx;

    if (ctx == NULL) return;
    ctxt->nodemem = -1;

#ifdef LIBXML_VALID_ENABLED
    if (ctxt->validate && ctxt->wellFormed &&
        ctxt->myDoc && ctxt->myDoc->intSubset)
        ctxt->valid &= xmlValidateOneElement(&ctxt->vctxt, ctxt->myDoc,
                                             ctxt->node);
#endif /* LIBXML_VALID_ENABLED */

    /*
     * 此节点解析结束。
     */
    nodePop(ctxt);
}

/**
 * 检测到实体xmlSAX2Reference()时调用。
 *
 * @param ctx  用户数据（XML解析器上下文）
 * @param name  实体名称
 */
void
xmlSAX2Reference(void *ctx, const xmlChar *name)
{
    xmlParserCtxtPtr ctxt = (xmlParserCtxtPtr) ctx;
    xmlNodePtr ret;

    if (ctx == NULL) return;
    ret = xmlNewReference(ctxt->myDoc, name);
    if (ret == NULL) {
        xmlSAX2ErrMemory(ctxt);
        return;
    }

    xmlSAX2AppendChild(ctxt, ret);
}

/**
 * 追加字符。
 *
 * @param ctxt  解析器上下文
 * @param ch  xmlChar字符串
 * @param len  xmlChar的数量
 * @param type  文本或cdata
 */
static void
xmlSAX2Text(xmlParserCtxtPtr ctxt, const xmlChar *ch, int len,
            xmlElementType type)
{
    xmlNodePtr lastChild;

    if (ctxt == NULL) return;
    /*
     * 处理数据（如果有）。如果没有子节点，将其添加为内容，
     * 否则如果最后一个子节点是文本，则连接它，
     * 否则创建一个文本类型的新节点。
     */

    if (ctxt->node == NULL) {
        return;
    }
    lastChild = ctxt->node->last;

    /*
     * 在非常大的元素情况下，我们需要一个加速机制。
     * 在结构中使用属性！！！
     */
    if (lastChild == NULL) {
        if (type == XML_TEXT_NODE)
            lastChild = xmlSAX2TextNode(ctxt, ch, len);
        else
            lastChild = xmlNewCDataBlock(ctxt->myDoc, ch, len);
	if (lastChild != NULL) {
	    ctxt->node->children = lastChild;
	    ctxt->node->last = lastChild;
	    lastChild->parent = ctxt->node;
	    lastChild->doc = ctxt->node->doc;
	    ctxt->nodelen = len;
	    ctxt->nodemem = len + 1;
	} else {
	    xmlSAX2ErrMemory(ctxt);
	    return;
	}
    } else {
	int coalesceText = (lastChild != NULL) &&
	    (lastChild->type == type) &&
	    (((ctxt->html) && (type != XML_TEXT_NODE)) ||
             (lastChild->name == xmlStringText));
	if ((coalesceText) && (ctxt->nodemem > 0)) {
            int maxLength = (ctxt->options & XML_PARSE_HUGE) ?
                            XML_MAX_HUGE_LENGTH :
                            XML_MAX_TEXT_LENGTH;

	    /*
	     * 维护nodelen和nodemem的全部意义在于，
	     * xmlTextConcat太昂贵，即计算长度、重新分配新缓冲区、
	     * 移动数据、追加ch。在这里我们尝试最小化realloc()的使用，
	     * 避免反复复制和重新计算长度。
	     */
	    if (lastChild->content == (xmlChar *)&(lastChild->properties)) {
		lastChild->content = xmlStrdup(lastChild->content);
		lastChild->properties = NULL;
	    } else if ((ctxt->nodemem == ctxt->nodelen + 1) &&
	               (xmlDictOwns(ctxt->dict, lastChild->content))) {
		lastChild->content = xmlStrdup(lastChild->content);
	    }
	    if (lastChild->content == NULL) {
		xmlSAX2ErrMemory(ctxt);
		return;
 	    }
            if ((len > maxLength) || (ctxt->nodelen > maxLength - len)) {
                xmlFatalErr(ctxt, XML_ERR_RESOURCE_LIMIT,
                            "Text node too long, try XML_PARSE_HUGE");
                xmlHaltParser(ctxt);
                return;
            }
	    if (ctxt->nodelen + len >= ctxt->nodemem) {
		xmlChar *newbuf;
		int size;

		size = ctxt->nodemem > INT_MAX - len ?
                       INT_MAX :
                       ctxt->nodemem + len;
		size = size > INT_MAX / 2 ? INT_MAX : size * 2;
                newbuf = (xmlChar *) xmlRealloc(lastChild->content,size);
		if (newbuf == NULL) {
		    xmlSAX2ErrMemory(ctxt);
		    return;
		}
		ctxt->nodemem = size;
		lastChild->content = newbuf;
	    }
	    memcpy(&lastChild->content[ctxt->nodelen], ch, len);
	    ctxt->nodelen += len;
	    lastChild->content[ctxt->nodelen] = 0;
	} else if (coalesceText) {
	    if (xmlTextConcat(lastChild, ch, len)) {
		xmlSAX2ErrMemory(ctxt);
	    }
	    if (ctxt->node->children != NULL) {
		ctxt->nodelen = xmlStrlen(lastChild->content);
		ctxt->nodemem = ctxt->nodelen + 1;
	    }
	} else {
	    /* 混合内容，第一次 */
            if (type == XML_TEXT_NODE) {
                lastChild = xmlSAX2TextNode(ctxt, ch, len);
                if (lastChild != NULL)
                    lastChild->doc = ctxt->myDoc;
            } else
                lastChild = xmlNewCDataBlock(ctxt->myDoc, ch, len);
	    if (lastChild == NULL) {
                xmlSAX2ErrMemory(ctxt);
            } else {
		xmlSAX2AppendChild(ctxt, lastChild);
		if (ctxt->node->children != NULL) {
		    ctxt->nodelen = len;
		    ctxt->nodemem = len + 1;
		}
	    }
	}
    }

    if ((lastChild != NULL) &&
        (type == XML_TEXT_NODE) &&
        (ctxt->linenumbers) &&
        (ctxt->input != NULL)) {
        if ((unsigned) ctxt->input->line < (unsigned) USHRT_MAX)
            lastChild->line = ctxt->input->line;
        else {
            lastChild->line = USHRT_MAX;
            if (ctxt->options & XML_PARSE_BIG_LINES)
                lastChild->psvi = XML_INT_TO_PTR(ctxt->input->line);
        }
    }
}

/**
 * 从解析器接收一些字符。
 *
 * @param ctx  用户数据（XML解析器上下文）
 * @param ch  xmlChar字符串
 * @param len  xmlChar的数量
 */
void
xmlSAX2Characters(void *ctx, const xmlChar *ch, int len)
{
    xmlSAX2Text((xmlParserCtxtPtr) ctx, ch, len, XML_TEXT_NODE);
}

/**
 * 从解析器接收一些可忽略的空白。
 * 未使用：默认情况下DOM构建将使用xmlSAX2Characters()
 *
 * @param ctx  用户数据（XML解析器上下文）
 * @param ch  xmlChar字符串
 * @param len  xmlChar的数量
 */
void
xmlSAX2IgnorableWhitespace(void *ctx ATTRIBUTE_UNUSED, const xmlChar *ch ATTRIBUTE_UNUSED, int len ATTRIBUTE_UNUSED)
{
}

/**
 * 已解析处理指令。
 *
 * @param ctx  用户数据（XML解析器上下文）
 * @param target  目标名称
 * @param data  PI数据
 */
void
xmlSAX2ProcessingInstruction(void *ctx, const xmlChar *target,
                      const xmlChar *data)
{
    xmlParserCtxtPtr ctxt = (xmlParserCtxtPtr) ctx;
    xmlNodePtr ret;

    if (ctx == NULL) return;

    ret = xmlNewDocPI(ctxt->myDoc, target, data);
    if (ret == NULL) {
        xmlSAX2ErrMemory(ctxt);
        return;
    }

    xmlSAX2AppendChild(ctxt, ret);
}

/**
 * 已解析xmlSAX2Comment()。
 *
 * @param ctx  用户数据（XML解析器上下文）
 * @param value  xmlSAX2Comment()内容
 */
void
xmlSAX2Comment(void *ctx, const xmlChar *value)
{
    xmlParserCtxtPtr ctxt = (xmlParserCtxtPtr) ctx;
    xmlNodePtr ret;

    if (ctx == NULL) return;

    ret = xmlNewDocComment(ctxt->myDoc, value);
    if (ret == NULL) {
        xmlSAX2ErrMemory(ctxt);
        return;
    }

    xmlSAX2AppendChild(ctxt, ret);
}

/**
 * 解析pcdata块时调用
 *
 * @param ctx  用户数据（XML解析器上下文）
 * @param value  pcdata内容
 * @param len  块长度
 */
void
xmlSAX2CDataBlock(void *ctx, const xmlChar *value, int len)
{
    xmlSAX2Text((xmlParserCtxtPtr) ctx, value, len, XML_CDATA_SECTION_NODE);
}

#ifdef LIBXML_SAX1_ENABLED
/**
 * 无效果。
 *
 * @deprecated 使用解析器选项XML_PARSE_SAX1。
 *
 * @param version  版本，必须为2
 * @returns 成功时返回2，错误时返回-1。
 */
int
xmlSAXDefaultVersion(int version)
{
    if (version != 2)
        return(-1);
    return(2);
}
#endif /* LIBXML_SAX1_ENABLED */

/**
 * 根据版本初始化默认XML SAX处理器
 *
 * @param hdlr  SAX处理器
 * @param version  版本，1或2
 * @returns 成功时返回0，错误时返回-1。
 */
int
xmlSAXVersion(xmlSAXHandler *hdlr, int version)
{
    if (hdlr == NULL) return(-1);
    if (version == 2) {
	hdlr->startElementNs = xmlSAX2StartElementNs;
	hdlr->endElementNs = xmlSAX2EndElementNs;
	hdlr->serror = NULL;
	hdlr->initialized = XML_SAX2_MAGIC;
#ifdef LIBXML_SAX1_ENABLED
    } else if (version == 1) {
	hdlr->initialized = 1;
#endif /* LIBXML_SAX1_ENABLED */
    } else
        return(-1);
#ifdef LIBXML_SAX1_ENABLED
    hdlr->startElement = xmlSAX2StartElement;
    hdlr->endElement = xmlSAX2EndElement;
#else
    hdlr->startElement = NULL;
    hdlr->endElement = NULL;
#endif /* LIBXML_SAX1_ENABLED */
    hdlr->internalSubset = xmlSAX2InternalSubset;
    hdlr->externalSubset = xmlSAX2ExternalSubset;
    hdlr->isStandalone = xmlSAX2IsStandalone;
    hdlr->hasInternalSubset = xmlSAX2HasInternalSubset;
    hdlr->hasExternalSubset = xmlSAX2HasExternalSubset;
    hdlr->resolveEntity = xmlSAX2ResolveEntity;
    hdlr->getEntity = xmlSAX2GetEntity;
    hdlr->getParameterEntity = xmlSAX2GetParameterEntity;
    hdlr->entityDecl = xmlSAX2EntityDecl;
    hdlr->attributeDecl = xmlSAX2AttributeDecl;
    hdlr->elementDecl = xmlSAX2ElementDecl;
    hdlr->notationDecl = xmlSAX2NotationDecl;
    hdlr->unparsedEntityDecl = xmlSAX2UnparsedEntityDecl;
    hdlr->setDocumentLocator = xmlSAX2SetDocumentLocator;
    hdlr->startDocument = xmlSAX2StartDocument;
    hdlr->endDocument = xmlSAX2EndDocument;
    hdlr->reference = xmlSAX2Reference;
    hdlr->characters = xmlSAX2Characters;
    hdlr->cdataBlock = xmlSAX2CDataBlock;
    hdlr->ignorableWhitespace = xmlSAX2Characters;
    hdlr->processingInstruction = xmlSAX2ProcessingInstruction;
    hdlr->comment = xmlSAX2Comment;
    hdlr->warning = xmlParserWarning;
    hdlr->error = xmlParserError;
    hdlr->fatalError = xmlParserError;

    return(0);
}

/**
 * 初始化默认XML SAX2处理器
 *
 * @param hdlr  SAX处理器
 * @param warning  如果非零则设置处理器警告过程的标志
 */
void
xmlSAX2InitDefaultSAXHandler(xmlSAXHandler *hdlr, int warning)
{
    if ((hdlr == NULL) || (hdlr->initialized != 0))
	return;

    xmlSAXVersion(hdlr, 2);
    if (warning == 0)
	hdlr->warning = NULL;
}

/**
 * 初始化默认SAX2处理器
 *
 * @deprecated 此函数是无操作的。调用xmlInitParser()来初始化库。
 *
 */
void
xmlDefaultSAXHandlerInit(void)
{
}

#ifdef LIBXML_HTML_ENABLED

/**
 * 初始化默认HTML SAX2处理器
 *
 * @param hdlr  SAX处理器
 */
void
xmlSAX2InitHtmlDefaultSAXHandler(xmlSAXHandler *hdlr)
{
    if ((hdlr == NULL) || (hdlr->initialized != 0))
	return;

    hdlr->internalSubset = xmlSAX2InternalSubset;
    hdlr->externalSubset = NULL;
    hdlr->isStandalone = NULL;
    hdlr->hasInternalSubset = NULL;
    hdlr->hasExternalSubset = NULL;
    hdlr->resolveEntity = NULL;
    hdlr->getEntity = xmlSAX2GetEntity;
    hdlr->getParameterEntity = NULL;
    hdlr->entityDecl = NULL;
    hdlr->attributeDecl = NULL;
    hdlr->elementDecl = NULL;
    hdlr->notationDecl = NULL;
    hdlr->unparsedEntityDecl = NULL;
    hdlr->setDocumentLocator = xmlSAX2SetDocumentLocator;
    hdlr->startDocument = xmlSAX2StartDocument;
    hdlr->endDocument = xmlSAX2EndDocument;
    hdlr->startElement = xmlSAX2StartElement;
    hdlr->endElement = xmlSAX2EndElement;
    hdlr->reference = NULL;
    hdlr->characters = xmlSAX2Characters;
    hdlr->cdataBlock = xmlSAX2CDataBlock;
    hdlr->ignorableWhitespace = xmlSAX2IgnorableWhitespace;
    hdlr->processingInstruction = xmlSAX2ProcessingInstruction;
    hdlr->comment = xmlSAX2Comment;
    hdlr->warning = xmlParserWarning;
    hdlr->error = xmlParserError;
    hdlr->fatalError = xmlParserError;

    hdlr->initialized = 1;
}

/**
 * @deprecated 此函数是无操作的。调用xmlInitParser()来初始化库。
 */
void
htmlDefaultSAXHandlerInit(void)
{
}

#endif /* LIBXML_HTML_ENABLED */
